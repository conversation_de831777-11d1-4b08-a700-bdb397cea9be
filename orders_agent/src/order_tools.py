#!/usr/bin/env python3
"""
Order Query Tools para Orders Agent

Herramientas especializadas para consultar información de órdenes.
Implementa funciones que pueden ser utilizadas por el agente para responder
preguntas sobre órdenes en lenguaje natural.
"""

import logging
from typing import Dict, List, Any, Optional
from datetime import datetime, date, timedelta
import json

from database import orders_db_manager

logger = logging.getLogger(__name__)


async def search_orders_by_status_function(status: str) -> str:
    """
    Función para buscar órdenes por estado
    
    Args:
        status: Estado de las órdenes (pending, completed, cancelled, processing, shipped)
        
    Returns:
        JSON string con las órdenes encontradas
    """
    try:
        # Normalizar el estado
        status_map = {
            'pendiente': 'pending',
            'pendientes': 'pending',
            'completada': 'completed',
            'completadas': 'completed',
            'cancelada': 'cancelled',
            'canceladas': 'cancelled',
            'procesando': 'processing',
            'enviada': 'shipped',
            'enviadas': 'shipped'
        }
        
        normalized_status = status_map.get(status.lower(), status.lower())



        # Buscar órdenes
        orders = await orders_db_manager.get_orders_by_status(normalized_status)
        
        if not orders:
            return json.dumps({
                'success': True,
                'message': f'No se encontraron órdenes con estado "{status}"',
                'orders': [],
                'count': 0
            }, ensure_ascii=False, default=str)
        
        # Formatear respuesta
        formatted_orders = []
        for order in orders:
            formatted_order = {
                'numero_orden': order['order_number'],
                'estado': order['status'],
                'cliente': order['customer_name'],
                'fecha_orden': order['order_date'].strftime('%Y-%m-%d %H:%M'),
                'fecha_entrega': order['delivery_date'].strftime('%Y-%m-%d') if order['delivery_date'] else None,
                'monto_total': float(order['total_amount']),
                'moneda': order['currency'],
                'productos': [
                    {
                        'nombre': item['product_name'],
                        'cantidad': item['quantity'],
                        'precio_unitario': float(item['unit_price']),
                        'precio_total': float(item['total_price'])
                    }
                    for item in order['items']
                ],
                'notas': order['notes']
            }
            formatted_orders.append(formatted_order)
        
        return json.dumps({
            'success': True,
            'message': f'Se encontraron {len(orders)} órdenes con estado "{status}"',
            'orders': formatted_orders,
            'count': len(orders)
        }, ensure_ascii=False, default=str)
        
    except Exception as e:
        logger.error(f"Error buscando órdenes por estado: {e}")
        return json.dumps({
            'success': False,
            'error': f'Error al buscar órdenes: {str(e)}',
            'orders': [],
            'count': 0
        }, ensure_ascii=False)

async def search_orders_by_date_function(date_query: str) -> str:
    """
    Función para buscar órdenes por fecha

    Args:
        date_query: Consulta de fecha (ej: "hoy", "ayer", "2025-01-10", "última semana")

    Returns:
        JSON string con las órdenes encontradas
    """
    try:

        today = date.today()
        start_date = None
        end_date = None
        
        # Interpretar la consulta de fecha
        date_query_lower = date_query.lower()
        
        if date_query_lower in ['hoy', 'today']:
            start_date = end_date = today
        elif date_query_lower in ['ayer', 'yesterday']:
            start_date = end_date = today - timedelta(days=1)
        elif date_query_lower in ['esta semana', 'this week']:
            start_date = today - timedelta(days=today.weekday())
            end_date = today
        elif date_query_lower in ['última semana', 'last week']:
            start_date = today - timedelta(days=today.weekday() + 7)
            end_date = today - timedelta(days=today.weekday() + 1)
        elif date_query_lower in ['este mes', 'this month']:
            start_date = today.replace(day=1)
            end_date = today
        else:
            # Intentar parsear como fecha específica
            try:
                parsed_date = datetime.strptime(date_query, '%Y-%m-%d').date()
                start_date = end_date = parsed_date
            except ValueError:
                return json.dumps({
                    'success': False,
                    'error': f'Formato de fecha no reconocido: {date_query}. Use formatos como "hoy", "ayer", "2025-01-10"',
                    'orders': [],
                    'count': 0
                }, ensure_ascii=False)
        
        # Buscar órdenes
        orders = await orders_db_manager.get_orders_by_date_range(start_date, end_date)
        
        if not orders:
            return json.dumps({
                'success': True,
                'message': f'No se encontraron órdenes para "{date_query}"',
                'orders': [],
                'count': 0
            }, ensure_ascii=False, default=str)
        
        # Formatear respuesta
        formatted_orders = []
        for order in orders:
            formatted_order = {
                'numero_orden': order['order_number'],
                'estado': order['status'],
                'cliente': order['customer_name'],
                'fecha_orden': order['order_date'].strftime('%Y-%m-%d %H:%M'),
                'fecha_entrega': order['delivery_date'].strftime('%Y-%m-%d') if order['delivery_date'] else None,
                'monto_total': float(order['total_amount']),
                'moneda': order['currency'],
                'productos': [
                    {
                        'nombre': item['product_name'],
                        'cantidad': item['quantity'],
                        'precio_unitario': float(item['unit_price']),
                        'precio_total': float(item['total_price'])
                    }
                    for item in order['items']
                ],
                'notas': order['notes']
            }
            formatted_orders.append(formatted_order)
        
        return json.dumps({
            'success': True,
            'message': f'Se encontraron {len(orders)} órdenes para "{date_query}"',
            'orders': formatted_orders,
            'count': len(orders),
            'date_range': {
                'start': start_date.strftime('%Y-%m-%d'),
                'end': end_date.strftime('%Y-%m-%d')
            }
        }, ensure_ascii=False, default=str)
        
    except Exception as e:
        logger.error(f"Error buscando órdenes por fecha: {e}")
        return json.dumps({
            'success': False,
            'error': f'Error al buscar órdenes: {str(e)}',
            'orders': [],
            'count': 0
        }, ensure_ascii=False)
async def search_orders_by_customer_function(customer_name: str) -> str:
    """
    Función para buscar órdenes por cliente

    Args:
        customer_name: Nombre del cliente (búsqueda parcial)

    Returns:
        JSON string con las órdenes encontradas
    """
    try:

        # Buscar órdenes
        orders = await orders_db_manager.get_orders_by_customer(customer_name)
        
        if not orders:
            return json.dumps({
                'success': True,
                'message': f'No se encontraron órdenes para el cliente "{customer_name}"',
                'orders': [],
                'count': 0
            }, ensure_ascii=False, default=str)
        
        # Formatear respuesta
        formatted_orders = []
        for order in orders:
            formatted_order = {
                'numero_orden': order['order_number'],
                'estado': order['status'],
                'cliente': order['customer_name'],
                'email': order['customer_email'],
                'telefono': order['customer_phone'],
                'fecha_orden': order['order_date'].strftime('%Y-%m-%d %H:%M'),
                'fecha_entrega': order['delivery_date'].strftime('%Y-%m-%d') if order['delivery_date'] else None,
                'monto_total': float(order['total_amount']),
                'moneda': order['currency'],
                'productos': [
                    {
                        'nombre': item['product_name'],
                        'cantidad': item['quantity'],
                        'precio_unitario': float(item['unit_price']),
                        'precio_total': float(item['total_price'])
                    }
                    for item in order['items']
                ],
                'notas': order['notes']
            }
            formatted_orders.append(formatted_order)
        
        return json.dumps({
            'success': True,
            'message': f'Se encontraron {len(orders)} órdenes para el cliente "{customer_name}"',
            'orders': formatted_orders,
            'count': len(orders)
        }, ensure_ascii=False, default=str)
        
    except Exception as e:
        logger.error(f"Error buscando órdenes por cliente: {e}")
        return json.dumps({
            'success': False,
            'error': f'Error al buscar órdenes: {str(e)}',
            'orders': [],
            'count': 0
        }, ensure_ascii=False)


async def get_order_statistics_function() -> str:
    """
    Función para obtener estadísticas de órdenes

    Returns:
        JSON string con estadísticas generales
    """
    try:
        # Asegurar que la base de datos esté inicializada
        if not orders_db_manager.pool:
            await orders_db_manager.initialize()
        stats = await orders_db_manager.get_order_statistics()
        
        return json.dumps({
            'success': True,
            'message': 'Estadísticas de órdenes obtenidas correctamente',
            'statistics': {
                'total_ordenes': stats['total_orders'],
                'ingresos_totales': stats['total_sales'],
                'valor_promedio_orden': stats['average_order_value'],
                'ordenes_por_estado': stats['status_breakdown']
            }
        }, ensure_ascii=False, default=str)
        
    except Exception as e:
        logger.error(f"Error obteniendo estadísticas: {e}")
        return json.dumps({
            'success': False,
            'error': f'Error al obtener estadísticas: {str(e)}',
            'statistics': {}
        }, ensure_ascii=False)


# Lista de herramientas disponibles para el agente
order_query_tools = [
    {
        'name': 'search_orders_by_status',
        'function': search_orders_by_status_function,
        'description': 'Buscar órdenes por estado (pending, completed, cancelled, etc.)'
    },
    {
        'name': 'search_orders_by_date',
        'function': search_orders_by_date_function,
        'description': 'Buscar órdenes por fecha o rango de fechas'
    },
    {
        'name': 'search_orders_by_customer',
        'function': search_orders_by_customer_function,
        'description': 'Buscar órdenes por nombre de cliente'
    },
    {
        'name': 'get_order_statistics',
        'function': get_order_statistics_function,
        'description': 'Obtener estadísticas generales de órdenes'
    }
]