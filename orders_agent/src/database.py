#!/usr/bin/env python3
"""
Database Manager para Orders Agent

Gestiona la conexión a PostgreSQL y operaciones específicas para órdenes.
Utiliza la misma configuración de base de datos que el qa_agent.
"""

import os
import logging
import asyncio
from typing import Dict, List, Any, Optional
from datetime import datetime, date
import asyncpg
from pathlib import Path

# Configurar logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# Cargar variables de entorno desde el archivo .env del qa_agent
env_file = Path(__file__).parent.parent.parent / "qa_agent" / ".env"
if env_file.exists():
    try:
        from dotenv import load_dotenv
        load_dotenv(env_file)
    except ImportError:
        logger.warning("python-dotenv no está disponible. Usando variables de entorno del sistema.")
        # Cargar manualmente las variables más importantes
        if env_file.exists():
            with open(env_file, 'r') as f:
                for line in f:
                    line = line.strip()
                    if line and not line.startswith('#') and '=' in line:
                        key, value = line.split('=', 1)
                        os.environ.setdefault(key.strip(), value.strip())


class OrdersDatabaseManager:
    """Gestor de base de datos para órdenes"""

    _instance = None
    _initialized = False
    _initializing = False

    def __new__(cls, database_url: str = None):
        if cls._instance is None:
            cls._instance = super().__new__(cls)
        return cls._instance

    def __init__(self, database_url: str = None):
        """
        Inicializar el gestor de base de datos para órdenes

        Args:
            database_url: URL de conexión a PostgreSQL
        """
        if self._initialized:
            return

        self.database_url = database_url or os.getenv("DATABASE_URL")
        if not self.database_url:
            raise ValueError("DATABASE_URL no está configurada")

        # Convertir URL de SQLAlchemy a asyncpg si es necesario
        if self.database_url.startswith("postgresql+asyncpg://"):
            self.database_url = self.database_url.replace("postgresql+asyncpg://", "postgresql://")

        self.pool = None
        self.schema_name = "agente_v5"
        self._lock = asyncio.Lock()
        self._initialized = True
    
    async def initialize(self):
        """Inicializar el pool de conexiones y crear las tablas de órdenes"""
        async with self._lock:
            # Evitar inicialización múltiple
            if self.pool is not None:
                logger.info("Base de datos ya inicializada, reutilizando conexión existente")
                return  # Ya está inicializada

            if self._initializing:
                logger.info("Inicialización en progreso, esperando...")
                # Esperar a que termine la inicialización en curso
                while self._initializing and self.pool is None:
                    await asyncio.sleep(0.1)
                return

            self._initializing = True
            try:
                logger.info("Inicializando pool de conexiones de base de datos...")
                # Crear pool de conexiones con configuración optimizada
                self.pool = await asyncpg.create_pool(
                    self.database_url,
                    min_size=1,
                    max_size=3,  # Reducir el tamaño máximo del pool
                    command_timeout=30,
                    server_settings={
                        'application_name': 'orders_agent'
                    }
                )

                # Crear esquema y tablas
                await self.create_schema()
                await self.create_orders_tables()

                logger.info("Base de datos de órdenes inicializada correctamente")

            except Exception as e:
                logger.error(f"Error inicializando base de datos de órdenes: {e}")
                if self.pool:
                    await self.pool.close()
                    self.pool = None
                raise
            finally:
                self._initializing = False
    
    async def close(self):
        """Cerrar el pool de conexiones"""
        if self.pool:
            await self.pool.close()
    
    async def create_schema(self):
        """Crear el esquema agente_v5 si no existe"""
        async with self.pool.acquire() as conn:
            # Crear esquema
            await conn.execute(f"CREATE SCHEMA IF NOT EXISTS {self.schema_name};")
            logger.info(f"Esquema {self.schema_name} creado/verificado")
    
    async def create_orders_tables(self):
        """Crear las tablas necesarias para órdenes"""
        async with self.pool.acquire() as conn:
            # Tabla principal de órdenes
            await conn.execute(f"""
                CREATE TABLE IF NOT EXISTS {self.schema_name}.orders (
                    order_id SERIAL PRIMARY KEY,
                    order_number VARCHAR(50) UNIQUE NOT NULL,
                    status VARCHAR(20) NOT NULL DEFAULT 'pending',
                    customer_name VARCHAR(255) NOT NULL,
                    customer_email VARCHAR(255),
                    customer_phone VARCHAR(50),
                    order_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    delivery_date DATE,
                    total_amount DECIMAL(10,2) NOT NULL,
                    currency VARCHAR(3) DEFAULT 'USD',
                    notes TEXT,
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
                );
            """)
            
            # Tabla de items de órdenes
            await conn.execute(f"""
                CREATE TABLE IF NOT EXISTS {self.schema_name}.order_items (
                    item_id SERIAL PRIMARY KEY,
                    order_id INTEGER REFERENCES {self.schema_name}.orders(order_id) ON DELETE CASCADE,
                    product_name VARCHAR(255) NOT NULL,
                    product_sku VARCHAR(100),
                    quantity INTEGER NOT NULL DEFAULT 1,
                    unit_price DECIMAL(10,2) NOT NULL,
                    total_price DECIMAL(10,2) NOT NULL,
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
                );
            """)
            
            # Índices para mejorar rendimiento
            await conn.execute(f"""
                CREATE INDEX IF NOT EXISTS idx_orders_status 
                ON {self.schema_name}.orders(status);
            """)
            
            await conn.execute(f"""
                CREATE INDEX IF NOT EXISTS idx_orders_date 
                ON {self.schema_name}.orders(order_date);
            """)
            
            await conn.execute(f"""
                CREATE INDEX IF NOT EXISTS idx_orders_customer 
                ON {self.schema_name}.orders(customer_name);
            """)
            
            logger.info("Tablas de órdenes creadas/verificadas")    
    async def _ensure_connection(self):
        """Asegurar que la conexión esté disponible"""
        if self.pool is None:
            await self.initialize()
        if self.pool is None:
            raise RuntimeError("No se pudo establecer conexión con la base de datos")

    async def get_orders_by_status(self, status: str, limit: int = 50) -> List[Dict[str, Any]]:
        """
        Obtener órdenes por estado

        Args:
            status: Estado de las órdenes (pending, completed, cancelled, etc.)
            limit: Número máximo de órdenes a retornar

        Returns:
            Lista de órdenes con sus items
        """
        await self._ensure_connection()
        async with self.pool.acquire() as conn:
            # Obtener órdenes
            orders = await conn.fetch(f"""
                SELECT 
                    order_id, order_number, status, customer_name, customer_email,
                    customer_phone, order_date, delivery_date, total_amount, 
                    currency, notes
                FROM {self.schema_name}.orders 
                WHERE status = $1 
                ORDER BY order_date DESC 
                LIMIT $2
            """, status, limit)
            
            result = []
            for order in orders:
                # Obtener items de cada orden
                items = await conn.fetch(f"""
                    SELECT product_name, product_sku, quantity, unit_price, total_price
                    FROM {self.schema_name}.order_items 
                    WHERE order_id = $1
                """, order['order_id'])
                
                order_dict = dict(order)
                order_dict['items'] = [dict(item) for item in items]
                result.append(order_dict)
            
            return result
    
    async def get_orders_by_date_range(self, start_date: date, end_date: date = None, limit: int = 50) -> List[Dict[str, Any]]:
        """
        Obtener órdenes por rango de fechas

        Args:
            start_date: Fecha de inicio
            end_date: Fecha de fin (opcional, por defecto hoy)
            limit: Número máximo de órdenes a retornar

        Returns:
            Lista de órdenes con sus items
        """
        if end_date is None:
            end_date = date.today()

        await self._ensure_connection()
        async with self.pool.acquire() as conn:
            orders = await conn.fetch(f"""
                SELECT 
                    order_id, order_number, status, customer_name, customer_email,
                    customer_phone, order_date, delivery_date, total_amount, 
                    currency, notes
                FROM {self.schema_name}.orders 
                WHERE DATE(order_date) BETWEEN $1 AND $2
                ORDER BY order_date DESC 
                LIMIT $3
            """, start_date, end_date, limit)
            
            result = []
            for order in orders:
                items = await conn.fetch(f"""
                    SELECT product_name, product_sku, quantity, unit_price, total_price
                    FROM {self.schema_name}.order_items 
                    WHERE order_id = $1
                """, order['order_id'])
                
                order_dict = dict(order)
                order_dict['items'] = [dict(item) for item in items]
                result.append(order_dict)
            
            return result    
    async def get_orders_by_customer(self, customer_name: str, limit: int = 50) -> List[Dict[str, Any]]:
        """
        Obtener órdenes por cliente

        Args:
            customer_name: Nombre del cliente (búsqueda parcial)
            limit: Número máximo de órdenes a retornar

        Returns:
            Lista de órdenes con sus items
        """
        await self._ensure_connection()
        async with self.pool.acquire() as conn:
            orders = await conn.fetch(f"""
                SELECT 
                    order_id, order_number, status, customer_name, customer_email,
                    customer_phone, order_date, delivery_date, total_amount, 
                    currency, notes
                FROM {self.schema_name}.orders 
                WHERE customer_name ILIKE $1
                ORDER BY order_date DESC 
                LIMIT $2
            """, f"%{customer_name}%", limit)
            
            result = []
            for order in orders:
                items = await conn.fetch(f"""
                    SELECT product_name, product_sku, quantity, unit_price, total_price
                    FROM {self.schema_name}.order_items 
                    WHERE order_id = $1
                """, order['order_id'])
                
                order_dict = dict(order)
                order_dict['items'] = [dict(item) for item in items]
                result.append(order_dict)
            
            return result
    
    async def get_order_statistics(self) -> Dict[str, Any]:
        """
        Obtener estadísticas generales de órdenes

        Returns:
            Diccionario con estadísticas
        """
        await self._ensure_connection()
        async with self.pool.acquire() as conn:
            # Contar órdenes por estado
            status_counts = await conn.fetch(f"""
                SELECT status, COUNT(*) as count
                FROM {self.schema_name}.orders 
                GROUP BY status
            """)
            
            # Total de órdenes y monto
            totals = await conn.fetchrow(f"""
                SELECT 
                    COUNT(*) as total_orders,
                    SUM(total_amount) as total_revenue,
                    AVG(total_amount) as avg_order_value
                FROM {self.schema_name}.orders
            """)
            
            # Órdenes de hoy
            today_orders = await conn.fetchrow(f"""
                SELECT COUNT(*) as today_orders
                FROM {self.schema_name}.orders 
                WHERE DATE(order_date) = CURRENT_DATE
            """)
            
            return {
                'status_counts': {row['status']: row['count'] for row in status_counts},
                'total_orders': totals['total_orders'] or 0,
                'total_revenue': float(totals['total_revenue'] or 0),
                'avg_order_value': float(totals['avg_order_value'] or 0),
                'today_orders': today_orders['today_orders'] or 0
            }    
    async def insert_sample_orders(self):
        """Insertar datos de ejemplo para pruebas"""
        await self._ensure_connection()
        sample_orders = [
            {
                'order_number': 'ORD-2025-001',
                'status': 'pending',
                'customer_name': 'María García',
                'customer_email': '<EMAIL>',
                'customer_phone': '******-0101',
                'total_amount': 150.00,
                'delivery_date': '2025-01-15',
                'notes': 'Entrega urgente',
                'items': [
                    {'product_name': 'Laptop HP', 'product_sku': 'HP-001', 'quantity': 1, 'unit_price': 150.00}
                ]
            },
            {
                'order_number': 'ORD-2025-002',
                'status': 'completed',
                'customer_name': 'Juan Pérez',
                'customer_email': '<EMAIL>',
                'customer_phone': '******-0102',
                'total_amount': 75.50,
                'delivery_date': '2025-01-12',
                'notes': 'Cliente frecuente',
                'items': [
                    {'product_name': 'Mouse Inalámbrico', 'product_sku': 'MSE-001', 'quantity': 2, 'unit_price': 25.00},
                    {'product_name': 'Teclado Mecánico', 'product_sku': 'KBD-001', 'quantity': 1, 'unit_price': 25.50}
                ]
            },
            {
                'order_number': 'ORD-2025-003',
                'status': 'pending',
                'customer_name': 'Ana López',
                'customer_email': '<EMAIL>',
                'customer_phone': '******-0103',
                'total_amount': 299.99,
                'delivery_date': '2025-01-20',
                'notes': 'Verificar dirección de entrega',
                'items': [
                    {'product_name': 'Monitor 4K', 'product_sku': 'MON-001', 'quantity': 1, 'unit_price': 299.99}
                ]
            },
            {
                'order_number': 'ORD-2025-004',
                'status': 'shipped',
                'customer_name': 'Carlos Rodríguez',
                'customer_email': '<EMAIL>',
                'customer_phone': '******-0104',
                'total_amount': 89.99,
                'delivery_date': '2025-01-11',
                'notes': 'Envío express',
                'items': [
                    {'product_name': 'Auriculares Bluetooth', 'product_sku': 'AUR-001', 'quantity': 1, 'unit_price': 89.99}
                ]
            }
        ]        
        async with self.pool.acquire() as conn:
            for order_data in sample_orders:
                # Verificar si la orden ya existe
                existing = await conn.fetchrow(f"""
                    SELECT order_id FROM {self.schema_name}.orders 
                    WHERE order_number = $1
                """, order_data['order_number'])
                
                if existing:
                    continue
                
                # Convertir fecha de string a date object
                from datetime import datetime
                delivery_date = datetime.strptime(order_data['delivery_date'], '%Y-%m-%d').date()

                # Insertar orden
                order_id = await conn.fetchval(f"""
                    INSERT INTO {self.schema_name}.orders
                    (order_number, status, customer_name, customer_email, customer_phone,
                     total_amount, delivery_date, notes)
                    VALUES ($1, $2, $3, $4, $5, $6, $7, $8)
                    RETURNING order_id
                """, order_data['order_number'], order_data['status'], order_data['customer_name'],
                    order_data['customer_email'], order_data['customer_phone'],
                    order_data['total_amount'], delivery_date, order_data['notes'])
                
                # Insertar items
                for item in order_data['items']:
                    await conn.execute(f"""
                        INSERT INTO {self.schema_name}.order_items 
                        (order_id, product_name, product_sku, quantity, unit_price, total_price)
                        VALUES ($1, $2, $3, $4, $5, $6)
                    """, order_id, item['product_name'], item['product_sku'], 
                        item['quantity'], item['unit_price'], 
                        item['quantity'] * item['unit_price'])
        
        logger.info("Datos de ejemplo insertados correctamente")

    async def get_total_orders(self) -> int:
        """
        Obtener el número total de órdenes en la base de datos

        Returns:
            Número total de órdenes
        """
        await self._ensure_connection()
        async with self.pool.acquire() as conn:
            result = await conn.fetchval(f"""
                SELECT COUNT(*) FROM {self.schema_name}.orders
            """)
            return result or 0

    async def get_recent_orders(self, limit: int = 10) -> List[Dict[str, Any]]:
        """
        Obtener las órdenes más recientes

        Args:
            limit: Número máximo de órdenes a retornar

        Returns:
            Lista de órdenes con sus items
        """
        await self._ensure_connection()
        async with self.pool.acquire() as conn:
            # Obtener órdenes más recientes
            orders = await conn.fetch(f"""
                SELECT
                    order_id, order_number, status, customer_name, customer_email,
                    customer_phone, order_date, delivery_date, total_amount,
                    currency, notes
                FROM {self.schema_name}.orders
                ORDER BY order_date DESC
                LIMIT $1
            """, limit)

            result = []
            for order in orders:
                # Obtener items de cada orden
                items = await conn.fetch(f"""
                    SELECT product_name, product_sku, quantity, unit_price, total_price
                    FROM {self.schema_name}.order_items
                    WHERE order_id = $1
                """, order['order_id'])

                order_dict = dict(order)
                order_dict['items'] = [dict(item) for item in items]
                result.append(order_dict)

            return result




# Instancia global del gestor de base de datos
orders_db_manager = OrdersDatabaseManager()